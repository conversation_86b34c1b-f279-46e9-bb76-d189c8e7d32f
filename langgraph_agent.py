import asyncio
import json
from typing import Dict, Any, List, Optional
from langgraph import StateGraph, END
from langgraph.graph import Graph
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.tools import tool
import httpx
from pydantic import BaseModel, Field

# OpenRouter configuration
OPENROUTER_API_KEY = "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"
OPENROUTER_MODEL = "moonshotai/kimi-k2"

class AgentState(BaseModel):
    messages: List[Dict[str, Any]] = Field(default_factory=list)
    thinking_steps: List[str] = Field(default_factory=list)
    current_step: int = 0
    final_answer: Optional[str] = None

class SequentialThinkingMCP:
    """MCP client for Sequential Thinking server"""
    
    def __init__(self):
        self.base_url = "http://localhost:3000"  # Default MCP server port
    
    async def start_thinking(self, problem: str) -> Dict[str, Any]:
        """Start a new thinking sequence"""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/thinking/start",
                json={"problem": problem}
            )
            return response.json()
    
    async def add_step(self, session_id: str, step: str) -> Dict[str, Any]:
        """Add a thinking step"""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/thinking/step",
                json={"session_id": session_id, "step": step}
            )
            return response.json()
    
    async def get_summary(self, session_id: str) -> Dict[str, Any]:
        """Get thinking summary"""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/thinking/summary/{session_id}"
            )
            return response.json()

class LangGraphAgent:
    def __init__(self):
        # Initialize OpenRouter LLM
        self.llm = ChatOpenAI(
            model=OPENROUTER_MODEL,
            openai_api_key=OPENROUTER_API_KEY,
            openai_api_base="https://openrouter.ai/api/v1",
            default_headers={
                "HTTP-Referer": "https://localhost:3000",
                "X-Title": "LangGraph Sequential Thinking Agent"
            }
        )
        
        # Initialize MCP client
        self.mcp_client = SequentialThinkingMCP()
        
        # Build the graph
        self.graph = self._build_graph()
    
    def _build_graph(self) -> Graph:
        """Build the LangGraph workflow"""
        workflow = StateGraph(AgentState)
        
        # Add nodes
        workflow.add_node("analyze_problem", self._analyze_problem)
        workflow.add_node("sequential_thinking", self._sequential_thinking)
        workflow.add_node("synthesize_answer", self._synthesize_answer)
        
        # Add edges
        workflow.add_edge("analyze_problem", "sequential_thinking")
        workflow.add_edge("sequential_thinking", "synthesize_answer")
        workflow.add_edge("synthesize_answer", END)
        
        # Set entry point
        workflow.set_entry_point("analyze_problem")
        
        return workflow.compile()
    
    async def _analyze_problem(self, state: AgentState) -> AgentState:
        """Analyze the problem and break it down"""
        last_message = state.messages[-1]["content"] if state.messages else ""
        
        analysis_prompt = f"""
        Analyze this problem and break it down into logical thinking steps:
        
        Problem: {last_message}
        
        Provide a structured analysis of what needs to be considered.
        """
        
        response = await self.llm.ainvoke([HumanMessage(content=analysis_prompt)])
        
        # Start MCP thinking session
        mcp_response = await self.mcp_client.start_thinking(last_message)
        
        state.messages.append({
            "role": "assistant",
            "content": f"Analysis: {response.content}",
            "mcp_session": mcp_response.get("session_id")
        })
        
        return state
    
    async def _sequential_thinking(self, state: AgentState) -> AgentState:
        """Perform sequential thinking using MCP"""
        session_id = state.messages[-1].get("mcp_session")
        problem = state.messages[0]["content"] if state.messages else ""
        
        thinking_prompt = f"""
        Think through this problem step by step:
        
        Problem: {problem}
        
        Provide your reasoning in clear, sequential steps. Each step should build on the previous one.
        """
        
        response = await self.llm.ainvoke([HumanMessage(content=thinking_prompt)])
        
        # Extract thinking steps and add to MCP
        thinking_steps = response.content.split('\n')
        for i, step in enumerate(thinking_steps):
            if step.strip():
                await self.mcp_client.add_step(session_id, step.strip())
                state.thinking_steps.append(step.strip())
        
        state.current_step = len(state.thinking_steps)
        
        return state
    
    async def _synthesize_answer(self, state: AgentState) -> AgentState:
        """Synthesize final answer using MCP summary"""
        session_id = state.messages[-1].get("mcp_session")
        
        # Get MCP summary
        summary = await self.mcp_client.get_summary(session_id)
        
        synthesis_prompt = f"""
        Based on the sequential thinking process, provide a clear, final answer:
        
        Thinking Steps:
        {chr(10).join(state.thinking_steps)}
        
        MCP Summary: {summary.get('summary', '')}
        
        Provide a concise, well-reasoned final answer.
        """
        
        response = await self.llm.ainvoke([HumanMessage(content=synthesis_prompt)])
        
        state.final_answer = response.content
        state.messages.append({
            "role": "assistant",
            "content": response.content,
            "type": "final_answer"
        })
        
        return state
    
    async def run(self, user_input: str) -> Dict[str, Any]:
        """Run the agent with user input"""
        initial_state = AgentState(
            messages=[{"role": "user", "content": user_input}]
        )
        
        final_state = await self.graph.ainvoke(initial_state)
        
        return {
            "final_answer": final_state.final_answer,
            "thinking_steps": final_state.thinking_steps,
            "messages": final_state.messages
        }

# Example usage
async def main():
    agent = LangGraphAgent()
    
    # Test the agent
    result = await agent.run(
        "What are the key considerations when designing a scalable microservices architecture?"
    )
    
    print("Final Answer:", result["final_answer"])
    print("\nThinking Steps:")
    for i, step in enumerate(result["thinking_steps"], 1):
        print(f"{i}. {step}")

if __name__ == "__main__":
    asyncio.run(main())